import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bloomg_flutter/features/face_verification/view/face_video_capture_page.dart';

/// Test to verify the Provider/BLoC dependency injection fix
/// 
/// This test simulates the app lifecycle changes that previously caused
/// the "Could not find the correct Provider<FaceVideoCaptureBloc>" error.
void main() {
  group('FaceVideoCapturePage Provider Fix Tests', () {
    testWidgets('should handle app lifecycle changes without provider errors', 
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: const FaceVideoCapturePage(),
        ),
      );

      // Verify the widget builds successfully
      expect(find.byType(FaceVideoCapturePage), findsOneWidget);

      // Simulate app lifecycle state changes that previously caused errors
      final binding = TestWidgetsFlutterBinding.ensureInitialized();
      
      // Simulate app being paused (this previously caused the error)
      binding.defaultBinaryMessenger.setMockMethodCallHandler(
        SystemChannels.lifecycle,
        (MethodCall methodCall) async {
          if (methodCall.method == 'routeUpdated') {
            return null;
          }
          return null;
        },
      );

      // Trigger lifecycle state change to paused
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        SystemChannels.lifecycle.name,
        SystemChannels.lifecycle.codec.encodeMessage('AppLifecycleState.paused'),
        (data) {},
      );

      // Pump the widget to process the lifecycle change
      await tester.pump();

      // Trigger lifecycle state change to resumed
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        SystemChannels.lifecycle.name,
        SystemChannels.lifecycle.codec.encodeMessage('AppLifecycleState.resumed'),
        (data) {},
      );

      // Pump the widget to process the lifecycle change
      await tester.pump();

      // If we reach this point without exceptions, the fix is working
      expect(find.byType(FaceVideoCapturePage), findsOneWidget);
    });

    testWidgets('should safely handle BLoC access when widget is disposed', 
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: const FaceVideoCapturePage(),
        ),
      );

      // Verify the widget builds successfully
      expect(find.byType(FaceVideoCapturePage), findsOneWidget);

      // Navigate away to trigger dispose
      await tester.pumpWidget(
        MaterialApp(
          home: Container(), // Different widget
        ),
      );

      // Pump to complete the dispose process
      await tester.pump();

      // If we reach this point without exceptions, disposal is working correctly
      expect(find.byType(FaceVideoCapturePage), findsNothing);
    });
  });
}
